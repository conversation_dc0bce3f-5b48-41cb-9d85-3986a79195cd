<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础功能</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    plain
					    size="mini"
					    type="warning"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义主题</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag text="标签">
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="success"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="error"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">圆形标签</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    plain
						shape="circle"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
						shape="circle"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">镂空标签</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    plain
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
					    plain
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="success"
					    plain
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="error"
					    plain
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">镂空带背景色</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    plain
					    plainFill
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
					    plain
					    plainFill
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="success"
					    plain
					    plainFill
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="error"
					    plain
					    plainFill
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义尺寸</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    plain
					    size="mini"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="success"
					    plain
					    size="large"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">可关闭标签</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    size="mini"
					    closable
					    :show="close1"
					    @close="close1 = false"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
					    closable
						:show="close2"
					    @close="close2 = false"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="success"
					    plain
					    size="large"
					    closable
						:show="close3"
					    @close="close3 = false"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">带图片和图标</text>
			<view class="u-demo-block__content">
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    size="mini"
					    icon="map"
					    plain
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="warning"
					    icon="tags-fill"
					>
					</u-tag>
				</view>
				<view class="u-page__tag-item">
					<u-tag
					    text="标签"
					    type="success"
					    plain
					    size="large"
					    icon="https://cdn.uviewui.com/uview/example/tag.png"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">单选标签</text>
			<view class="u-demo-block__content">
				<view
				    class="u-page__tag-item"
				    v-for="(item, index) in radios"
				    :key="index"
				>
					<u-tag
					    :text="`选项${index + 1}`"
					    :plain="!item.checked"
					    type="warning"
					    :name="index"
					    @click="radioClick"
					>
					</u-tag>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">多选标签</text>
			<view class="u-demo-block__content">
				<view
				    class="u-page__tag-item"
				    v-for="(item, index) in checkboxs"
				    :key="index"
				>
					<u-tag
					    :text="`选项${index + 1}`"
					    :plain="!item.checked"
					    type="warning"
					    :name="index"
					    @click="checkboxClick"
					>
					</u-tag>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				close1: true,
				close2: true,
				close3: true,
				radios: [{
						checked: true
					},
					{
						checked: false
					},
					{
						checked: false
					}
				],
				checkboxs: [{
						checked: true
					},
					{
						checked: false
					},
					{
						checked: false
					}
				]
			}
		},
		methods: {
			radioClick(name) {
				this.radios.map((item, index) => {
					item.checked = index === name ? true : false
				})
			},
			checkboxClick(name) {
				this.checkboxs[name].checked = !this.checkboxs[name].checked
			}
		}
	}
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}

	.u-demo-block__content {
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
	}
</style>
