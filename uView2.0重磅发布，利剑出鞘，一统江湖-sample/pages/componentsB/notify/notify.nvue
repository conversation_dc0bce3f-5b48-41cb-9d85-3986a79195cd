<template>
	<view class="u-page">
		<u-gap
			height="30"
			bgColor="#fff"
		></u-gap>
		<u-cell-group>
			<u-cell
				:titleStyle="{fontWeight: 500}"
				@click="openNotify(item.notifyData)"
				:title="item.title"
				v-for="(item, index) in list"
				:key="index"
				isLink
			>
				<image
					slot="icon"
					class="u-cell-icon"
					:src="item.iconUrl"
					mode="widthFix"
				></image>
			</u-cell>
		</u-cell-group>
		<u-notify ref="uNotify"></u-notify>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				notifyData: {
					message: 'notify顶部提示',
					type: 'primary',
					color: '#ffffff',
					bgColor: '',
					fontSize: 15,
					duration: 3000,
				},
				list: [{
						notifyData: {
							message: 'notify顶部提示',
							type: 'primary',
							color: '#ffffff',
							bgColor: '',
							fontSize: 15,
							duration: 3000
						},
						title: '主要通知',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/main.png'
					}, {
						notifyData: {
							message: 'notify顶部提示',
							type: 'success',
							color: '#ffffff',
							bgColor: '',
							fontSize: 15,
							duration: 3000,
							safeAreaInsetTop: false
						},
						title: '成功通知',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/success.png'
					}, {
						notifyData: {
							message: 'notify顶部提示',
							type: 'error',
							color: '#ffffff',
							bgColor: '',
							fontSize: 14,
							duration: 3000,
							safeAreaInsetTop: false
						},
						title: '危险通知',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/error.png'
					}, {
						notifyData: {
							message: 'notify顶部提示',
							type: 'warning',
							color: '#ffffff',
							bgColor: '',
							fontSize: 15,
							duration: 3000,
							safeAreaInsetTop: false
						},
						title: '警告通知',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/warning.png'
					},
					{
						notifyData: {
							message: 'notify顶部提示',
							color: '#fff',
							bgColor: '#000',
							fontSize: 15,
							duration: 3000,
							safeAreaInsetTop: false
						},
						title: '自定义样式',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/customStyle.png'
					},
					{
						notifyData: {
							message: 'notify顶部提示',
							type: 'primary',
							color: '#ffffff',
							bgColor: '',
							fontSize: 15,
							duration: 6000,
							safeAreaInsetTop: false
						},
						title: '自定义时间',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/customTime.png'
					},
					{
						notifyData: {
							message: 'notify顶部提示',
							color: '#fff',
							bgColor: '',
							fontSize: 15,
							duration: 3000,
							safeAreaInsetTop: true
						},
						title: '插入状态栏高度',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/notify/height.png'
					}
				]
			}
		},
		onLoad() {},
		methods: {
			openNotify(params) {
				this.$refs.uNotify.show({
					...params
				})
				// 也可以通过主题形式调用，如：
				// this.$refs.uNotify.primary('Primary主题')
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		padding: 0;
	}
</style>
