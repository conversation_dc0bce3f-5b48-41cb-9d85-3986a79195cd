<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础用法</text>
			<view class="u-demo-block__content">
				<view class="u-page__upload-item">
				<u-upload
				    :fileList="fileList1"
				    @afterRead="afterRead"
				    @delete="deletePic"
				    name="1"
				    multiple
				    :maxCount="10"
				></u-upload>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">上传视频</text>
			<view class="u-demo-block__content">
				<view class="u-page__upload-item">
				<u-upload
				    :fileList="fileList2"
				    @afterRead="afterRead"
				    @delete="deletePic"
				    name="2"
				    multiple
				    :maxCount="10"
				    accept="video"
				></u-upload>
				</view>
			</view>
		</view>
			<view class="u-demo-block">
			<text class="u-demo-block__title">文件预览</text>
			<view class="u-demo-block__content">
				<view class="u-page__upload-item">
				<u-upload
				    :fileList="fileList3"
				    @afterRead="afterRead"
				    @delete="deletePic"
				    name="3"
				    multiple
				    :maxCount="10"
				    :previewFullImage="true"
				></u-upload>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">隐藏上传按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__upload-item">
				<u-upload
				    :fileList="fileList4"
				    @afterRead="afterRead"
				    @delete="deletePic"
				    name="4"
				    multiple
				    :maxCount="2"
				></u-upload>
				</view>
			</view>
		</view>
			<view class="u-demo-block">
			<text class="u-demo-block__title">限制上传数量</text>
			<view class="u-demo-block__content">
				<view class="u-page__upload-item">
				<u-upload
				    :fileList="fileList5"
				    @afterRead="afterRead"
				    @delete="deletePic"
				    name="5"
				    multiple
				    :maxCount="3"
				></u-upload>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义上传样式</text>
			<view class="u-demo-block__content">
				<view class="u-page__upload-item">
					<u-upload
						:fileList="fileList6"
						@afterRead="afterRead"
						@delete="deletePic"
						name="6"
						multiple
						:maxCount="1"
						width="250"
						height="150"
					>
						<image src="https://cdn.uviewui.com/uview/demo/upload/positive.png" mode="widthFix" style="width: 250px;height: 150px;"></image>
					</u-upload>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				fileList1: [],
				fileList2: [],
				fileList3: [{
					url: 'https://cdn.uviewui.com/uview/swiper/1.jpg',
				}],
				fileList4: [{
						url: 'https://cdn.uviewui.com/uview/swiper/1.jpg',
					},
					{
						url: 'https://cdn.uviewui.com/uview/swiper/1.jpg',
					}
				],
				fileList5: [],
				fileList6: [],
				fileList7: []
			}
		},
		onLoad() {
		},
		methods: {
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'http://www.example.com/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						formData: {
							user: 'test'
						},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data.data)
							}, 1000)
						}
					});
				})
			},
		},
	}
</script>

<style lang="scss">
	.u-page {
		&__upload-item{
			margin-top:5px;
		}
	}
</style>
