<template>
	<view class="u-page">
		<u-navbar
			title="上拉菜单"
			@leftClick="navigateBack"
			safeAreaInsetTop
			fixed
			placeholder
		></u-navbar>
		<u-cell-group>
			<u-cell
				@click="openSheet(index)"
				:title="item.title"
				v-for="(item, index) in list"
				:key="index"
				isLink
			>
				<image
					slot="icon"
					class="u-cell-icon"
					:src="item.iconUrl"
					mode="widthFix"
				></image>
			</u-cell>
		</u-cell-group>
		<u-action-sheet
			:show="show0"
			@close="close"
			@select="select"
			:actions="actions0"
			:closeOnClickOverlay="false"
		>
		</u-action-sheet>
		<u-action-sheet
			:show="show1"
			@close="show1 = false"
			:actions="actions1"
		>
		</u-action-sheet>
		<u-action-sheet
			:show="show2"
			@close="show2 = false"
			:actions="actions2"
			cancelText="取消"
		>
		</u-action-sheet>
		<u-action-sheet
			:show="show3"
			@close="show3 = false"
			:actions="actions3"
			description="这是一段描述文本,字号偏小,颜色偏淡"
		>
		</u-action-sheet>
		<u-action-sheet
			:show="show4"
			@close="show4 = false"
			title="标题位置"
			:round="10"
		>
			<text style="margin: 10px 20px 30px 20px; color: #303133; font-size: 15px;">这是一段通过slot传入的内容,您可以在此自定义操作面板</text>
		</u-action-sheet>
		<u-action-sheet
			:show="show5"
			@close="show5 = false"
			title="微信开放能力"
			:actions="actions5"
			@getuserinfo="getuserinfo"
		></u-action-sheet>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				show0: false,
				show1: false,
				show2: false,
				show3: false,
				show4: false,
				show5: false,
				actions0: [{
						name: '选项1',
					},
					{
						name: '选项2',
					},
					{
						name: '选项3',
						subname: '描述文本'
					},
				],
				actions1: [{
						name: '选项1',
					},
					{
						loading: true
					},
					{
						name: '选项被禁用',
						disabled: true
					},
				],
				actions2: [{
						name: '选项1',
					},
					{
						name: '选项2',
					},
					{
						name: '选项3',
					},
				],
				actions3: [{
						name: '选项1',
					},
					{
						name: '选项2',
					},
					{
						name: '选项3',
					},
				],
				actions5: [{
					name: '获取用户信息',
					openType: 'getUserInfo',
					color: uni.$u.color['success']
				}],
				list: [{
						title: '普通使用',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/actionSheet/custom.png'

					},
					{
						title: '设置状态',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/actionSheet/status.png'
					},
					{
						title: '显示取消按钮',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/actionSheet/cancel.png'
					},
					{
						title: '描述内容',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/actionSheet/desc.png'
					},
					{
						title: '显示标题(显示圆角)',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/actionSheet/title.png'
					},
					{
						title: '微信开放能力',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/actionSheet/open.png'
					}
				]
			}
		},
		onLoad() {

		},
		methods: {
			// 点击cell，判断显示哪个功能
			openSheet(index) {
				// #ifndef MP
				if (index === 5) return uni.$u.toast('请在微信内预览')
				// #endif
				this[`show${index}`] = true
			},
			getuserinfo(res) {
				uni.$u.toast(`用户名：${res.userInfo.nickName}`)
			},
			navigateBack() {
				uni.navigateBack()
			},
			close() {
				console.log('close');
				this['show0'] = false
			},
			select(e) {
				console.log('select', e);
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		padding: 0;
	}
</style>
