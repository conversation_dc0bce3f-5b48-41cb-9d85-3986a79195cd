export default [{
    groupName: '基础组件',
    groupName_en: 'Basic components',
    list: [{
        path: '/pages/componentsB/color/color',
        icon: 'color',
        title: 'Color 色彩',
        title_en: 'Color'
    }, {
        path: '/pages/componentsA/icon/icon',
        icon: 'icon',
        title: 'Icon 图标',
        title_en: 'Icon'
    }, {
        path: '/pages/componentsA/image/image',
        icon: 'image',
        title: 'Image 图片',
        title_en: 'Image'
    }, {
        path: '/pages/componentsA/button/button',
        icon: 'button',
        title: 'Button 按钮',
        title_en: 'Button'
    }, {
        path: '/pages/componentsC/text/text',
        icon: 'text',
        title: 'Text 文本',
        title_en: 'Text'
    }, {
        path: '/pages/componentsC/layout/layout',
        icon: 'layout',
        title: 'Layout 布局',
        title_en: 'Layout'
    }, {
        path: '/pages/componentsA/cell/cell',
        icon: 'cell',
        title: 'Cell 单元格',
        title_en: 'Cell'
    }, {
        path: '/pages/componentsB/badge/badge',
        icon: 'badge',
        title: 'Badge 徽标数',
        title_en: 'Badge'
    }, {
        path: '/pages/componentsB/tag/tag',
        icon: 'tag',
        title: 'Tag 标签',
        title_en: 'Tag'
    }, {
        path: '/pages/componentsA/loading-icon/loading-icon',
        icon: 'loading',
        title: 'Loading 加载动画',
        title_en: 'loading Icon'
    }, {
        path: '/pages/componentsA/loading-page/loading-page',
        icon: 'loading-page',
        title: 'Loading page 加载页',
        title_en: 'Loading Page'
    }]
},
{
    groupName: '表单组件',
    groupName_en: 'Form components',
    list: [{
        path: '/pages/componentsC/form/form',
        icon: 'form',
        title: 'Form 表单',
        title_en: 'Form'
    }, {
        path: '/pages/componentsC/calendar/calendar',
        icon: 'calendar',
        title: 'Calendar 日历',
        title_en: 'Calendar'
    }, {
        path: '/pages/componentsB/keyboard/keyboard',
        icon: 'keyboard',
        title: 'Keyboard 键盘',
        title_en: 'Keyboard'
    }, {
        path: '/pages/componentsC/picker/picker',
        icon: 'picker',
        title: 'Picker 选择器',
        title_en: 'Picker'
    }, {
        path: '/pages/componentsC/datetimePicker/datetimePicker',
        icon: 'datetimePicker',
        title: 'DatetimePicker 时间选择器',
        title_en: 'Picker'
    }, {
        path: '/pages/componentsA/rate/rate',
        icon: 'rate',
        title: 'Rate 评分',
        title_en: 'Rate'
    }, {
        path: '/pages/componentsB/search/search',
        icon: 'search',
        title: 'Search 搜索',
        title_en: 'Search'
    }, {
        path: '/pages/componentsB/numberBox/numberBox',
        icon: 'numberBox',
        title: 'NumberBox 步进器',
        title_en: 'NumberBox'
    }, {
        path: '/pages/componentsB/upload/upload',
        icon: 'upload',
        title: 'Upload 上传',
        title_en: 'Upload'
    }, {
        path: '/pages/componentsB/code/code',
        icon: 'code',
        title: 'Code 验证码倒计时',
        title_en: 'VerificationCode'
    }, {
        path: '/pages/componentsC/input/input',
        icon: 'field',
        title: 'Input 输入框',
        title_en: 'Input'
    }, {
        path: '/pages/componentsC/textarea/textarea',
        icon: 'textarea',
        title: 'Textarea 文本域',
        title_en: 'Textarea'
    }, {
        path: '/pages/componentsA/checkbox/checkbox',
        icon: 'checkbox',
        title: 'Checkbox 复选框',
        title_en: 'Checkbox'
    }, {
        path: '/pages/componentsA/radio/radio',
        icon: 'radio',
        title: 'Radio 单选框',
        title_en: 'Radio'
    }, {
        path: '/pages/componentsB/switch/switch',
        icon: 'switch',
        title: 'Switch 开关选择器',
        title_en: 'Switch'
    }, {
        path: '/pages/componentsB/slider/slider',
        icon: 'slider',
        title: 'Slider 滑动选择器',
        title_en: 'Slider'
    }, {
        path: '/pages/componentsC/album/album',
        icon: 'album',
        title: 'Album 相册',
        title_en: 'Album'
    }]
}, {
    groupName: '数据组件',
    groupName_en: 'Data components',
    list: [{
        path: '/pages/componentsC/list/list',
        icon: 'list',
        title: 'List 列表',
        title_en: 'List'
    }, {
        path: '/pages/componentsB/progress/progress',
        icon: 'progress',
        title: 'Progress 进度条',
        title_en: 'Progress'
    },
    // {
    // 	path: '/pages/componentsC/table/table',
    // 	icon: 'table',
    // 	title: 'Table 表格（暂无）',
    // 	title_en: 'Table',
    // },
    {
        path: '/pages/componentsB/countDown/countDown',
        icon: 'countDown',
        title: 'CountDown 倒计时',
        title_en: 'CountDown'
    }, {
        path: '/pages/componentsB/countTo/countTo',
        icon: 'countTo',
        title: 'CountTo 数字滚动',
        title_en: 'CountTo'
    }]
}, {
    groupName: '反馈组件',
    groupName_en: 'Feedback components',
    list: [{
        path: '/pages/componentsC/tooltip/tooltip',
        icon: 'tooltip',
        title: 'Tooltip 长按提示',
        title_en: 'ActionSheet'
    }, {
        path: '/pages/componentsB/actionSheet/actionSheet',
        icon: 'actionSheet',
        title: 'ActionSheet 上拉菜单',
        title_en: 'ActionSheet'
    }, {
        path: '/pages/componentsB/alert/alert',
        icon: 'alert',
        title: 'Alert 警告提示',
        title_en: 'Alert'
    }, {
        path: '/pages/componentsB/toast/toast',
        icon: 'toast',
        title: 'Toast 消息提示',
        title_en: 'Toast'
    }, {
        path: '/pages/componentsB/noticeBar/noticeBar',
        icon: 'noticeBar',
        title: 'NoticeBar 滚动通知',
        title_en: 'NoticeBar'
    }, {
        path: '/pages/componentsB/notify/notify',
        icon: 'notify',
        title: 'Notify 消息提示',
        title_en: 'Notify'
    }, {
        path: '/pages/componentsA/swipeAction/swipeAction',
        icon: 'swipeAction',
        title: 'SwipeAction 滑动单元格',
        title_en: 'SwipeAction'
    }, {
        path: '/pages/componentsB/collapse/collapse',
        icon: 'collapse',
        title: 'Collapse 折叠面板',
        title_en: 'Collapse'
    }, {
        path: '/pages/componentsA/popup/popup',
        icon: 'popup',
        title: 'Popup 弹出层',
        title_en: 'Popup'
    }, {
        path: '/pages/componentsC/modal/modal',
        icon: 'modal',
        title: 'Modal 模态框',
        title_en: 'Modal'
    }
        // {
        // 	path: '/pages/componentsA/fullScreen/fullScreen',
        // 	icon: 'pressingScreen',
        // 	title: 'fullScreen 压窗屏（暂无）',
        // 	title_en: 'fullScreen',
        // },
    ]
}, {
    groupName: '布局组件',
    groupName_en: 'Layout components',
    list: [{
        path: '/pages/componentsC/scrollList/scrollList',
        icon: 'scrollList',
        title: 'ScrollList 横向滚动列表',
        title_en: 'ScrollList'
    }, {
        path: '/pages/componentsA/line/line',
        icon: 'line',
        title: 'Line 线条',
        title_en: 'Line'
    }, {
        path: '/pages/componentsA/overlay/overlay',
        icon: 'mask',
        title: 'Overlay 遮罩层',
        title_en: 'Overlay'
    },
    // #ifndef MP-TOUTIAO
    {
        path: '/pages/componentsC/noNetwork/noNetwork',
        icon: 'noNetwork',
        title: 'NoNetwork 无网络提示',
        title_en: 'NoNetwork'
    },
    // #endif
    {
        path: '/pages/componentsA/grid/grid',
        icon: 'grid',
        title: 'Grid 宫格布局',
        title_en: 'Grid'
    }, {
        path: '/pages/componentsC/swiper/swiper',
        icon: 'swiper',
        title: 'Swiper 轮播图',
        title_en: 'Swiper'
    }, {
        path: '/pages/componentsC/skeleton/skeleton',
        icon: 'skeleton',
        title: 'Skeleton 骨架屏',
        title_en: 'Skeleton'
    }, {
        path: '/pages/componentsA/sticky/sticky',
        icon: 'sticky',
        title: 'Sticky 吸顶',
        title_en: 'Sticky'
    },
    {
        path: '/pages/componentsA/divider/divider',
        icon: 'divider',
        title: 'Divider 分割线',
        title_en: 'Divider'
    }
    ]
},
{
    groupName: '导航组件',
    groupName_en: 'Navigation components',
    list: [
        // {
        // 	path: '/pages/componentsB/dropdown/dropdown',
        // 	icon: 'dropdown',
        // 	title: 'Dropdown 下拉菜单',
        // 	title_en: 'Dropdown',
        // },
        {
            path: '/pages/componentsB/tabbar/tabbar',
            icon: 'tabbar',
            title: 'Tabbar 底部导航栏',
            title_en: 'Tabbar'
        }, {
            path: '/pages/componentsA/backtop/backtop',
            icon: 'backTop',
            title: 'BackTop 返回顶部',
            title_en: 'BackTop'
        }, {
            path: '/pages/componentsC/navbar/navbar',
            icon: 'navbar',
            title: 'Navbar 导航栏',
            title_en: 'Navbar'
        }, {
            path: '/pages/componentsC/tabs/tabs',
            icon: 'tabs',
            title: 'Tabs 标签',
            title_en: 'Tabs'
        },
        // // #ifndef MP-ALIPAY
        // {
        // 	path: '/pages/template/order/order',
        // 	icon: 'tabsSwiper',
        // 	title: 'TabsSwiper 全屏选项卡（暂无）',
        // 	title_en: 'TabsSwiper',
        // },
        // // #endif
        {
            path: '/pages/componentsC/subsection/subsection',
            icon: 'subsection',
            title: 'Subsection 分段器',
            title_en: 'Subsection'
        }, {
            path: '/pages/componentsC/indexList/indexList',
            icon: 'indexList',
            title: 'IndexList 索引列表',
            title_en: 'IndexList'
        }, {
            path: '/pages/componentsC/steps/steps',
            icon: 'steps',
            title: 'Steps 步骤条',
            title_en: 'Steps'
        }, {
            path: '/pages/componentsA/empty/empty',
            icon: 'empty',
            title: 'Empty 内容为空',
            title_en: 'Empty'
        }
    ]
}, {
    groupName: '其他组件',
    groupName_en: 'Other components',
    list: [{
        path: '/pages/componentsB/parse/parse',
        icon: 'parse',
        title: 'Parse 富文本解析器',
        title_en: 'Parse'
    }, {
        path: '/pages/componentsC/codeInput/codeInput',
        icon: 'messageInput',
        title: 'CodeInput 验证码输入',
        title_en: 'CodeInput'
    },
    // {
    // 	path: '/pages/componentsC/avatarCropper/avatarCropper',
    // 	icon: 'avatarCropper',
    // 	title: 'AvatarCropper 头像裁剪（暂无）',
    // 	title_en: 'AvatarCropper',
    // },
    {
        path: '/pages/componentsC/loadmore/loadmore',
        icon: 'loadmore',
        title: 'Loadmore 加载更多',
        title_en: 'Loadmore'
    }, {
        path: '/pages/componentsC/readMore/readMore',
        icon: 'readMore',
        title: 'ReadMore 展开阅读更多',
        title_en: 'ReadMore'
    },
    // {
    // 	path: '/pages/componentsA/lazyLoad/lazyLoad',
    // 	icon: 'lazyLoad',
    // 	title: 'LazyLoad 懒加载（暂无）',
    // 	title_en: 'LazyLoad',
    // },
    {
        path: '/pages/componentsA/gap/gap',
        icon: 'gap',
        title: 'Gap 间隔槽',
        title_en: 'Gap'
    }, {
        path: '/pages/componentsC/avatar/avatar',
        icon: 'avatar',
        title: 'Avatar 头像',
        title_en: 'Avatar'
    }, {
        path: '/pages/componentsA/link/link',
        icon: 'link',
        title: 'Link 超链接',
        title_en: 'Link'
    }, {
        path: '/pages/componentsA/transition/transition',
        icon: 'transition',
        title: 'transition 动画',
        title_en: '动画'
    }]
}
]
