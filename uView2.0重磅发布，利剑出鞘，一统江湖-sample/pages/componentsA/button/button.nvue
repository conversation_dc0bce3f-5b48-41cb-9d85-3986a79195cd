<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">按钮类型</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    text="默认按钮"
					    size="normal"
					    type="info"
						@click="click"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="成功按钮"
					    size="normal"
					    type="success"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="危险按钮"
					    size="normal"
					    type="error"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="主要按钮"
					    size="normal"
					    type="primary"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="警告按钮"
					    size="normal"
					    type="warning"
					></u-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">镂空按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    text="镂空按钮"
					    size="normal"
					    type="info"
					    plain
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="镂空按钮"
					    size="normal"
					    type="success"
					    plain
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="镂空按钮"
					    size="normal"
					    type="error"
					    plain
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="镂空按钮"
					    size="normal"
					    type="primary"
					    plain
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="镂空按钮"
					    size="normal"
					    type="warning"
					    plain
					></u-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">细边按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    text="细边按钮"
					    size="normal"
					    type="info"
					    plain
					    hairline
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="细边按钮"
					    size="normal"
					    type="success"
					    plain
					    hairline
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="细边按钮"
					    size="normal"
					    type="error"
					    plain
					    hairline
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="细边按钮"
					    size="normal"
					    type="primary"
					    plain
					    hairline
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="细边按钮"
					    size="normal"
					    type="warning"
					    plain
					    hairline
					></u-button>
				</view>
			</view>
		</view>

		<view class="u-demo-block">
			<text class="u-demo-block__title">禁用按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="info"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="success"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="error"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="primary"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="warning"
					></u-button>
				</view>
			</view>
		</view>

		<view class="u-demo-block">
			<text class="u-demo-block__title">加载中</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    loadingText="加载中"
					    size="normal"
						loading
					    loadingMode="circle"
					    type="success"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    class="button-layout__item"
					    loadingText="加载中"
					    size="normal"
					    loading
					    type="error"
					></u-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">按钮图标&按钮形状</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    text="按钮图标"
					    size="normal"
					    icon="map"
					    plain
					    type="warning"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="按钮图标"
					    size="normal"
					    plain
					    shape="circle"
					    type="success"
					></u-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义颜色</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    text="渐变色按钮"
					    size="normal"
					    color="linear-gradient(to right, rgb(66, 83, 216), rgb(213, 51, 186))"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="渐变色按钮"
					    size="normal"
					    color="linear-gradient(to right, rgb(220, 194, 11), rgb(4, 151, 99))"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="青绿色按钮"
					    size="normal"
					    color="rgb(10, 185, 156)"
					></u-button>
				</view>
			</view>
		</view>

		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义大小</text>
			<view class="u-demo-block__content" style="padding-bottom: 15px; flex-direction: column;align-items: stretch;flex-wrap: nowrap;">
				<u-button
				    text="超大尺寸"
				    size="large"
				    type="success"
				></u-button>
			</view>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<u-button
					    text="普通尺寸"
					    size="normal"
					    type="error"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    text="小型尺寸"
					    size="small"
					    type="primary"
					></u-button>
				</view>
				<view class="u-page__button-item">
					<u-button
					    class="button-layout__item"
					    text="超小尺寸"
					    size="mini"
					    type="warning"
					></u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// type: 'default',
				// disabled: false
			}
		},
		onLoad() {
			setTimeout(() => {
				this.type = 'primary'
				this.disabled = true
			}, 2000)
		},
		methods: {
			click() {
				console.log('click');
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		&__button-item {
			margin: 0 15px 15px 0;
		}
	}

	.u-demo-block__content {
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
	}
</style>
