<template>
	<view class="u-page">
		<u-navbar
			title="加载页"
			@leftClick="navigateBack"
			safeAreaInsetTop
			fixed
			placeholder
		></u-navbar>
		<u-gap
		    bgColor="#fff"
		    height="20"
		></u-gap>
		<u-cell-group>
			<u-cell
			    :titleStyle="{fontWeight: 500}"
			    @click="openLoadingPage(index)"
			    :title="item.title"
			    v-for="(item, index) in list"
			    :key="index"
			    isLink
			>
				<image
				    slot="icon"
				    class="u-cell-icon"
				    :src="item.iconUrl"
				    mode="widthFix"
				></image>
			</u-cell>
		</u-cell-group>
		<u-loading-page
		    :loadingText="loadingPageData.loadingText"
		    :image="loadingPageData.image"
			:iconSize="loadingPageData.iconSize"
		    :loadingMode="loadingPageData.loadingMode"
		    :bgColor="loadingPageData.bgColor"
		    :loading="loading"
		    :color="loadingPageData.color"
		    :loadingColor="loadingPageData.loadingColor"
		>
		</u-loading-page>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				loadingPageData: {
					// 自定义提示内容
					loadingText: '',
					// 自定义图片
					image: '',
					// 自定义加载动画模式
					loadingMode: '',
					// 自定义背景色
					bgColor: '#ffffff',
				},
				list: [{
						title: '自定义提示内容',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/loading-page/promptContent.png',
					},
					{
						title: '自定义图片',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/loading-page/customPicture.png',
					},
					{
						title: '自定义加载动画模式',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/loading-page/customMode.png',
					},
					{
						title: '自定义背景色',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/loading-page/customBgColor.png',

					},
				]
			}
		},
		methods: {
			navigateBack() {
				uni.navigateBack()
			},
			openLoadingPage(indexNum) {
				this.loadingPageData = {
					loadingText: '',
					image: '',
					loadingMode: '',
					bgColor: '#ffffff',
					iconSize: 28
				}
				if (indexNum == 0) {
					//自定义提示内容
					this.loadingPageData.loadingMode = 'semicircle'
					this.loadingPageData.loadingText = "Hello uView"
					this.loadingPageData.color = '#C8C8C8'
					this.loadingPageData.loadingColor = '#C8C8C8'
				} else if (indexNum == 1) {
					// 自定义图片
					this.loadingPageData.image = "/static/uview/common/logo.png"
					this.loadingPageData.loadingText = "uView UI"
					this.loadingPageData.iconSize = 40
					this.loadingPageData.color = '#C8C8C8'
					this.loadingPageData.loadingColor = '#C8C8C8'
				} else if (indexNum == 2) {
					// 自定义加载动画模式
					this.loadingPageData.loadingMode = 'circle'
					this.loadingPageData.loadingText = "uView UI"
					this.loadingPageData.color = '#C8C8C8'
					this.loadingPageData.loadingColor = '#C8C8C8'
				} else if (indexNum == 3) {
					// 自定义背景色
					this.loadingPageData.loadingMode = 'spinner'
					this.loadingPageData.bgColor = 'rgba(0, 0, 0, 0.3)'
					this.loadingPageData.loadingText = "uView UI"
					this.loadingPageData.color = '#eee'
					this.loadingPageData.loadingColor = '#ddd'
				}
				this.loading = true
				setTimeout(() => {
					this.loading = false
				}, 2000)
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		padding: 0;
	}
</style>
