<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础使用</text>
			<view class="u-demo-block__content">
				<u--textarea
					v-model="value1"
					placeholder="请输入内容"
				></u--textarea>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">字数统计</text>
			<view class="u-demo-block__content">
				<u--textarea
					v-model="value2"
					placeholder="请输入内容"
					count
				></u--textarea>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自动增高</text>
			<view class="u-demo-block__content">
				<u--textarea
					v-model="value3"
					placeholder="请输入内容"
					autoHeight
				></u--textarea>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">禁用状态</text>
			<view class="u-demo-block__content">
				<u--textarea
					v-model="value4"
					placeholder="文本域已被禁用"
					disabled
					count
				></u--textarea>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">下划线模式</text>
			<view class="u-demo-block__content">
				<u--textarea
					v-model="value5"
					placeholder="请输入内容"
					border="bottom"
				></u--textarea>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value1: '',
				value2: '统计字数',
				value3: '',
				value4: '',
				value5: ''
			}
		},
		methods: {
			formatter(value) {
				return value.replace(/[^0-9]/ig,"")
			}
		}
	}
</script>

<style lang="scss">

</style>
