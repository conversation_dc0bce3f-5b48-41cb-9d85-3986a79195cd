<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础功能</text>
			<u-swiper
				:list="list1"
				@change="change"
				@click="click"
			></u-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">带标题</text>
			<u-swiper
				:list="list2"
				keyName="image"
				showTitle
				:autoplay="false"
				circular
			></u-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">带指示器</text>
			<u-swiper
				:list="list3"
				indicator
				indicatorMode="line"
				circular
			></u-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">加载中</text>
			<u-swiper
				:list="list3"
				loading
			></u-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">嵌入视频</text>
			<u-swiper
				:list="list4"
				keyName="url"
				:autoplay="false"
			></u-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义指示器</text>
			<u-swiper
				:list="list5"
				@change="e => current = e.current"
				:autoplay="false"
			>
				<view
					slot="indicator"
					class="indicator"
				>
					<view
						class="indicator__dot"
						v-for="(item, index) in list5"
						:key="index"
						:class="[index === current && 'indicator__dot--active']"
					>
					</view>
				</view>
			</u-swiper>
			<u-gap
				bgColor="transparent"
				height="15"
			></u-gap>
			<u-swiper
				:list="list6"
				@change="e => currentNum = e.current"
				:autoplay="false"
				indicatorStyle="right: 20px"
			>
				<view
					slot="indicator"
					class="indicator-num"
				>
					<text class="indicator-num__text">{{ currentNum + 1 }}/{{ list6.length }}</text>
				</view>
			</u-swiper>
		</view>
		<!-- #ifndef APP-NVUE || MP-TOUTIAO -->
		<view class="u-demo-block">
			<text class="u-demo-block__title">卡片式</text>
			<u-swiper
				:list="list3"
				previousMargin="30"
				nextMargin="30"
				circular
				:autoplay="false"
				radius="5"
				bgColor="#ffffff"
			></u-swiper>
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				current: 0,
				currentNum: 0,
				list: [{
						// image: 'https://cdn.uviewui.com/uview/resources/video.mp4',
						image: 'https://cdn.uviewui.com/uview/swiper/swiper1.png',
						title: '昨夜星辰昨夜风，画楼西畔桂堂东',
						poster: 'https://cdn.uviewui.com/uview/swiper/swiper1.png'
					},
					{
						image: 'https://cdn.uviewui.com/uview/swiper/swiper2.png',
						title: '身无彩凤双飞翼，心有灵犀一点通'
					},
					{
						image: 'https://cdn.uviewui.com/uview/swiper/swiper3.png',
						title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
					}
				],
				list1: [
					'https://cdn.uviewui.com/uview/swiper/swiper1.png',
					'https://cdn.uviewui.com/uview/swiper/swiper2.png',
					'https://cdn.uviewui.com/uview/swiper/swiper3.png',
				],
				list2: [{
						image: 'https://cdn.uviewui.com/uview/swiper/swiper2.png',
						title: '昨夜星辰昨夜风，画楼西畔桂堂东',
						type: 'image',
					},
					{
						image: 'https://cdn.uviewui.com/uview/swiper/swiper1.png',
						title: '身无彩凤双飞翼，心有灵犀一点通'
					},
					{
						image: 'https://cdn.uviewui.com/uview/swiper/swiper3.png',
						title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
					}
				],
				list3: [
					'https://cdn.uviewui.com/uview/swiper/swiper3.png',
					'https://cdn.uviewui.com/uview/swiper/swiper2.png',
					'https://cdn.uviewui.com/uview/swiper/swiper1.png',
				],
				list4: [{
						url: 'https://cdn.uviewui.com/uview/resources/video.mp4',
						title: '昨夜星辰昨夜风，画楼西畔桂堂东',
						poster: 'https://cdn.uviewui.com/uview/swiper/swiper1.png',
					},
					{
						url: 'https://cdn.uviewui.com/uview/swiper/swiper2.png',
						title: '身无彩凤双飞翼，心有灵犀一点通'
					},
					{
						url: 'https://cdn.uviewui.com/uview/swiper/swiper3.png',
						title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
					}
				],
				list5: [
					'https://cdn.uviewui.com/uview/swiper/swiper3.png',
					'https://cdn.uviewui.com/uview/swiper/swiper2.png',
					'https://cdn.uviewui.com/uview/swiper/swiper1.png',
				],
				list6: [
					'https://cdn.uviewui.com/uview/swiper/swiper2.png',
					'https://cdn.uviewui.com/uview/swiper/swiper3.png',
					'https://cdn.uviewui.com/uview/swiper/swiper1.png',
				]
			}
		},
		methods: {
			change(e) {
				// console.log('change', e);
			},
			click(e) {
				console.log('click', e);
			}
		},
	}
</script>

<style lang="scss">
	.indicator {
		@include flex(row);
		justify-content: center;

		&__dot {
			height: 6px;
			width: 6px;
			border-radius: 100px;
			background-color: rgba(255, 255, 255, 0.35);
			margin: 0 5px;
			transition: background-color 0.3s;

			&--active {
				background-color: #ffffff;
			}
		}
	}

	.indicator-num {
		padding: 2px 0;
		background-color: rgba(0, 0, 0, 0.35);
		border-radius: 100px;
		width: 35px;
		@include flex;
		justify-content: center;

		&__text {
			color: #FFFFFF;
			font-size: 12px;
		}
	}
</style>
