<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础使用</text>
			<view class="u-demo-block__content">
				<u-loadmore
					status="loading"
					:isDot="true"
					:iconSize="17"
				></u-loadmore>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">无更多数据</text>
			<view class="u-demo-block__content">
				<u-loadmore
					:line="true"
					status="nomore"
				></u-loadmore>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">加载更多(点击触发事件)</text>
			<view class="u-demo-block__content">
				<u-loadmore
					:line="true"
					status="loadmore"
					@loadmore="loadmore"
				></u-loadmore>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义图标</text>
			<view class="u-demo-block__content">
				<u-loadmore
					status="loading"
					loadingIcon="circle"
				></u-loadmore>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">显示点</text>
			<view class="u-demo-block__content">
				<u-loadmore
					status="nomore"
					:isDot="true"
					:line="true"
					color="#909399"
				></u-loadmore>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义提示语</text>
			<view class="u-demo-block__content">
				<u-loadmore
					status="loading"
					loadingText="努力加载中,先喝杯茶"
					color="#909399"
				></u-loadmore>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义线条颜色</text>
			<view class="u-demo-block__content">
				<u-loadmore
					loadmoreText="看,我和别人不一样"
					color="#1CD29B"
					lineColor="#1CD29B"
					dashed
					:line="true"
				></u-loadmore>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			loadmore() {
				console.log('loadmore');
				uni.$u.toast('加载更多')
			}
		},
	}
</script>

<style lang="scss">

</style>
