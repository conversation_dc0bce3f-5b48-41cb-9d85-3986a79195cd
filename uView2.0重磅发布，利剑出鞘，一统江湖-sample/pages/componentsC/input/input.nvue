<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础使用</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="请输入内容"
					border="surround"
					v-model="value"
					@change="change"
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">可清空内容</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="请输入内容"
					border="surround"
					clearable
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">数字键盘</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="请输入内容"
					border="surround"
					type="number"
					clearable
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">密码类型</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="请输入内容"
					border="surround"
					password
					clearable
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">显示下划线</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="请输入内容"
					border="bottom"
					clearable
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">禁用状态</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="禁用状态"
					border="surround"
					disabled
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">圆形</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="请输入内容"
					border="surround"
					shape="circle"
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">前后图标</text>
			<view class="u-demo-block__content">
				<u--input
					placeholder="前置图标"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
				></u--input>
			</view>
			<view
				class="u-demo-block__content"
				style="margin-top: 15px;"
			>
				<u--input
					placeholder="后置图标"
					suffixIcon="map-fill"
					suffixIconStyle="color: #909399"
				></u--input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">前后插槽</text>
			<view class="u-demo-block__content">
				<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
				<!-- #ifndef APP-NVUE -->
				<u-input placeholder="前置插槽">
					<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					<u--input placeholder="前置插槽">
						<!-- #endif -->
						<u--text
							text="http://"
							slot="prefix"
							margin="0 3px 0 0"
							type="tips"
						></u--text>
						<!-- #ifndef APP-NVUE -->
				</u-input>
				<!-- #endif -->
				<!-- #ifdef APP-NVUE -->
				</u--input>
				<!-- #endif -->
			</view>
			<view
				class="u-demo-block__content"
				style="margin-top: 15px;"
			>
				<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
				<!-- #ifndef APP-NVUE -->
				<u-input placeholder="后置插槽">
					<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					<u--input placeholder="后置插槽">
						<!-- #endif -->
						<template slot="suffix">
							<u-code
								ref="uCode"
								@change="codeChange"
								seconds="20"
								changeText="X秒重新获取哈哈哈"
							></u-code>
							<u-button
								@tap="getCode"
								:text="tips"
								type="success"
								size="mini"
							></u-button>
						</template>
						<!-- #ifndef APP-NVUE -->
				</u-input>
				<!-- #endif -->
				<!-- #ifdef APP-NVUE -->
				</u--input>
				<!-- #endif -->
			</view>
		</view>
		<u-gap
			bgColor="#fff"
			height="50"
		></u-gap>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tips: '',
				value: ''
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			}
		}
	}
</script>

<style lang="scss">

</style>
