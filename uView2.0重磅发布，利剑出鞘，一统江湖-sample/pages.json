{
	// "condition": { //模式配置，仅开发期间生效
	// 	"current": 0, //当前激活的模式(list 的索引项)
	// 	"list": [{
	// 		"name": "test", //模式名称
	// 		"path": "pages/componentsA/test/test", //启动页面，必选
	// 		"query": "" //启动参数，在页面的onLoad函数里面得到
	// 	}]
	// },
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/example/components",
			"style": {
				"navigationBarTitleText": "uView UI"
			}
		}
	],
	"subPackages": [{
		"root": "pages/componentsA",
		"pages": [
			// 过渡动画
			{
				"path": "transition/transition",
				"style": {
					"navigationBarTitleText": "过渡动画"
				}
			},
			{
				"path": "test/test",
				"style": {
					"navigationBarTitleText": "测试"
				}
			},
			{
				"path": "icon/icon",
				"style": {
					"navigationBarTitleText": "图标"
				}
			},
			{
				"path": "cell/cell",
				"style": {
					"navigationBarTitleText": "单元格"
				}
			},
			{
				"path": "line/line",
				"style": {
					"navigationBarTitleText": "线条"
				}
			},
			{
				"path": "image/image",
				"style": {
					"navigationBarTitleText": "图片"
				}
			},
			{
				"path": "link/link",
				"style": {
					"navigationBarTitleText": "超链接"
				}
			},
			{
				"path": "button/button",
				"style": {
					"navigationBarTitleText": "按钮"
				}
			},
			{
				"path": "loading-icon/loading-icon",
				"style": {
					"navigationBarTitleText": "加载中图标"
				}
			},
			{
				"path": "overlay/overlay",
				"style": {
					"navigationBarTitleText": "遮罩层",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "loading-page/loading-page",
				"style": {
					"navigationBarTitleText": "加载页",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "popup/popup",
				"style": {
					"navigationBarTitleText": "弹窗",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "swipeAction/swipeAction",
				"style": {
					"navigationBarTitleText": "滑动单元格"
				}
			},
			{
				"path": "sticky/sticky",
				"style": {
					"navigationBarTitleText": "吸顶"
				}
			},
			{
				"path": "radio/radio",
				"style": {
					"navigationBarTitleText": "单选框"
				}
			},
			{
				"path": "checkbox/checkbox",
				"style": {
					"navigationBarTitleText": "复选框"
				}
			},
			{
				"path": "empty/empty",
				"style": {
					"navigationBarTitleText": "内容为空"
				}
			},
			{
				"path": "backtop/backtop",
				"style": {
					"navigationBarTitleText": "返回顶部"
				}
			},
			{
				"path": "divider/divider",
				"style": {
					"navigationBarTitleText": "分割线"
				}
			},
			{
				"path": "rate/rate",
				"style": {
					"navigationBarTitleText": "评分"
				}
			},
			{
				"path": "gap/gap",
				"style": {
					"navigationBarTitleText": "间隔槽"
				}
			},
			{
				"path": "grid/grid",
				"style": {
					"navigationBarTitleText": "宫格"
				}
			}
		]
	}, {
		"root": "pages/componentsB",
		"pages": [{
			"path": "dropdown/dropdown",
			"style": {
				"navigationBarTitleText": "下拉菜单"
			}
		}, {
			"path": "actionSheet/actionSheet",
			"style": {
				"navigationBarTitleText": "上拉菜单",
				"navigationStyle": "custom"
			}
		}, {
			"path": "parse/parse",
			"style": {
				"navigationBarTitleText": "富文本解析器"
			}
		}, {
			"path": "parse/jump",
			"style": {
				"navigationBarTitleText": "内部链接"
			}
		}, {
			"path": "toast/toast",
			"style": {
				"navigationBarTitleText": "提示消息"
			}
		}, {
			"path": "keyboard/keyboard",
			"style": {
				"navigationBarTitleText": "键盘",
				"navigationStyle": "custom"
			}
		}, {
			"path": "slider/slider",
			"style": {
				"navigationBarTitleText": "滑动选择器"
			}
		}, {
			"path": "upload/upload",
			"style": {
				"navigationBarTitleText": "上传"
			}
		}, {
			"path": "notify/notify",
			"style": {
				"navigationBarTitleText": "消息提示"
			}
		}, {
			"path": "countDown/countDown",
			"style": {
				"navigationBarTitleText": "倒计时"
			}
		}, {
			"path": "color/color",
			"style": {
				"navigationBarTitleText": "色彩"
			}
		}, {
			"path": "numberBox/numberBox",
			"style": {
				"navigationBarTitleText": "步进器"
			}
		}, {
			"path": "countTo/countTo",
			"style": {
				"navigationBarTitleText": "数字滚动"
			}
		}, {
			"path": "search/search",
			"style": {
				"navigationBarTitleText": "搜索"
			}
		}, {
			"path": "badge/badge",
			"style": {
				"navigationBarTitleText": "徽标数"
			}
		}, {
			"path": "tag/tag",
			"style": {
				"navigationBarTitleText": "标签"
			}
		}, {
			"path": "alert/alert",
			"style": {
				"navigationBarTitleText": "警告"
			}
		}, {
			"path": "switch/switch",
			"style": {
				"navigationBarTitleText": "开关"
			}
		}, {
			"path": "collapse/collapse",
			"style": {
				"navigationBarTitleText": "折叠面板"
			}
		}, {
			"path": "code/code",
			"style": {
				"navigationBarTitleText": "验证码"
			}
		}, {
			"path": "noticeBar/noticeBar",
			"style": {
				"navigationBarTitleText": "滚动通知"
			}
		}, {
			"path": "progress/progress",
			"style": {
				"navigationBarTitleText": "进度条"
			}
		}, {
			"path": "tabbar/tabbar",
			"style": {
				"navigationBarTitleText": "Tabbar"
			}
		}]
	}, {
		"root": "pages/componentsC",
		"pages": [{
			"path": "table/table",
			"style": {
				"navigationBarTitleText": "表格"
			}
		}, {
			"path": "form/form",
			"style": {
				"navigationBarTitleText": "表单",
				"navigationStyle": "custom"
			}
		}, {
			"path": "textarea/textarea",
			"style": {
				"navigationBarTitleText": "文本域"
			}
		}, {
			"path": "noNetwork/noNetwork",
			"style": {
				"navigationBarTitleText": "无网络提示"
			}
		}, {
			"path": "loadmore/loadmore",
			"style": {
				"navigationBarTitleText": "加载更多"
			}
		}, {
			"path": "text/text",
			"style": {
				"navigationBarTitleText": "文本"
			}
		}, {
			"path": "steps/steps",
			"style": {
				"navigationBarTitleText": "步骤条"
			}
		}, {
			"path": "navbar/navbar",
			"style": {
				"navigationBarTitleText": "导航栏",
				"navigationStyle": "custom"
			}
		}, {
			"path": "skeleton/skeleton",
			"style": {
				"navigationBarTitleText": "骨架屏"
			}
		}, {
			"path": "input/input",
			"style": {
				"navigationBarTitleText": "输入框"
			}
		}, {
			"path": "album/album",
			"style": {
				"navigationBarTitleText": "相册"
			}
		}, {
			"path": "avatar/avatar",
			"style": {
				"navigationBarTitleText": "头像"
			}
		}, {
			"path": "readMore/readMore",
			"style": {
				"navigationBarTitleText": "阅读更多"
			}
		}, {
			"path": "layout/layout",
			"style": {
				"navigationBarTitleText": "布局"
			}
		}, {
			"path": "indexList/indexList",
			"style": {
				"navigationBarTitleText": "索引列表"
			}
		}, {
			"path": "tooltip/tooltip",
			"style": {
				"navigationBarTitleText": "长按提示"
			}
		}, {
			"path": "tabs/tabs",
			"style": {
				"navigationBarTitleText": "标签"
			}
		}, {
			"path": "list/list",
			"style": {
				"navigationBarTitleText": "列表"
			}
		}, {
			"path": "swiper/swiper",
			"style": {
				"navigationBarTitleText": "轮播"
			}
		}, {
			"path": "scrollList/scrollList",
			"style": {
				"navigationBarTitleText": "横向滚动列表"
			}
		}, {
			"path": "codeInput/codeInput",
			"style": {
				"navigationBarTitleText": "验证码输入"
			}
		}, {
			"path": "modal/modal",
			"style": {
				"navigationBarTitleText": "模态框",
				"navigationStyle": "custom"
			}
		}, {
			"path": "picker/picker",
			"style": {
				"navigationBarTitleText": "选择器",
				"navigationStyle": "custom"
			}
		}, {
			"path": "calendar/calendar",
			"style": {
				"navigationBarTitleText": "日历",
				"navigationStyle": "custom"
			}
		}, {
			"path": "datetimePicker/datetimePicker",
			"style": {
				"navigationBarTitleText": "时间选择",
				"navigationStyle": "custom"
			}
		}, {
			"path": "subsection/subsection",
			"style": {
				"navigationBarTitleText": "分段器"
			}
		}]
	}],
	"preloadRule": {
		"pages/example/components": {
			"network": "all",
			"packages": ["pages/componentsA", "pages/componentsB"]
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uView",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	}
}
